import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import dayjs from 'dayjs';

export interface ExportColumn {
  title: string;
  dataIndex: string | string[];
  key: string;
  render?: (value: any, record: any) => string | number;
}

export interface ExportOptions {
  filename?: string;
  sheetName?: string;
  columns: ExportColumn[];
  data: any[];
  filters?: Record<string, any>;
  statisticType?: string;
  currentLang?: string;
}

/**
 * Exporte les données vers un fichier Excel
 */
export const exportToExcel = (options: ExportOptions) => {
  const {
    filename = 'export',
    sheetName = 'Données',
    columns,
    data,
    filters = {},
    statisticType = '',
    currentLang = 'fr'
  } = options;

  // Préparer les données pour l'export
  const exportData = data.map((record: any) => {
    const row: Record<string, any> = {};
    
    columns.forEach((column) => {
      let value: any;
      
      // Gérer les dataIndex complexes (tableaux)
      if (Array.isArray(column.dataIndex)) {
        value = column.dataIndex.reduce((obj, key) => obj?.[key], record);
      } else {
        value = record[column.dataIndex];
      }
      
      // Appliquer le render si défini
      if (column.render && typeof column.render === 'function') {
        value = column.render(value, record);
      }
      
      // Nettoyer la valeur pour l'export
      if (typeof value === 'string') {
        // Supprimer les balises HTML et les caractères spéciaux
        value = value.replace(/<[^>]*>/g, '').replace(/&[^;]+;/g, '');
        // Supprimer "DT" à la fin si présent
        value = value.replace(/\s*DT\s*$/i, '');
      }
      
      row[column.title] = value || '';
    });
    
    return row;
  });

  // Créer le workbook
  const workbook = XLSX.utils.book_new();
  
  // Créer la feuille de données
  const worksheet = XLSX.utils.json_to_sheet(exportData);
  
  // Ajouter des informations sur les filtres en haut de la feuille
  if (Object.keys(filters).length > 0) {
    const filterInfo = createFilterInfo(filters, currentLang);
    const filterRows = filterInfo.map(info => ({ [columns[0]?.title || 'Info']: info }));
    
    // Insérer les lignes de filtre au début
    XLSX.utils.sheet_add_json(worksheet, filterRows, { origin: 'A1' });
    XLSX.utils.sheet_add_json(worksheet, exportData, { origin: `A${filterRows.length + 2}` });
  }
  
  // Ajuster la largeur des colonnes
  const columnWidths = columns.map((col) => ({ wch: Math.max(col.title.length, 15) }));
  worksheet['!cols'] = columnWidths;
  
  // Ajouter la feuille au workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
  
  // Générer le nom de fichier avec timestamp
  const timestamp = dayjs().format('YYYY-MM-DD_HH-mm-ss');
  const finalFilename = `${filename}_${timestamp}.xlsx`;
  
  // Exporter le fichier
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  saveAs(blob, finalFilename);
};

/**
 * Crée les informations de filtre pour l'export
 */
const createFilterInfo = (filters: Record<string, any>, currentLang: string): string[] => {
  const filterInfo: string[] = [];
  
  filterInfo.push('=== FILTRES APPLIQUÉS ===');
  filterInfo.push('');
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      let filterLabel = getFilterLabel(key, currentLang);
      let filterValue = value;
      
      // Formater les dates
      if (key.includes('Date') && dayjs(value).isValid()) {
        filterValue = dayjs(value).format('DD/MM/YYYY');
      }
      
      filterInfo.push(`${filterLabel}: ${filterValue}`);
    }
  });
  
  filterInfo.push('');
  filterInfo.push(`Date d'export: ${dayjs().format('DD/MM/YYYY HH:mm:ss')}`);
  filterInfo.push('');
  
  return filterInfo;
};

/**
 * Obtient le label du filtre selon la langue
 */
const getFilterLabel = (key: string, currentLang: string): string => {
  const labels: Record<string, Record<string, string>> = {
    subscriptionType: {
      fr: 'Type d\'abonnement',
      en: 'Subscription Type',
      ar: 'نوع الاشتراك'
    },
    startDate: {
      fr: 'Date de début',
      en: 'Start Date',
      ar: 'تاريخ البداية'
    },
    endDate: {
      fr: 'Date de fin',
      en: 'End Date',
      ar: 'تاريخ النهاية'
    },
    salePeriod: {
      fr: 'Période de vente',
      en: 'Sale Period',
      ar: 'فترة البيع'
    },
    journey: {
      fr: 'Trajet',
      en: 'Journey',
      ar: 'الرحلة'
    },
    establishment: {
      fr: 'Établissement',
      en: 'Establishment',
      ar: 'المؤسسة'
    },
    lineId: {
      fr: 'Ligne',
      en: 'Line',
      ar: 'الخط'
    },
    agency: {
      fr: 'Agence',
      en: 'Agency',
      ar: 'الوكالة'
    },
    salePoint: {
      fr: 'Point de vente',
      en: 'Sale Point',
      ar: 'نقطة البيع'
    }
  };
  
  return labels[key]?.[currentLang] || key;
};

/**
 * Obtient le nom de fichier selon le type de statistique
 */
export const getExportFilename = (statisticType: string, currentLang: string): string => {
  const filenames: Record<string, Record<string, string>> = {
    '1': {
      fr: 'statistiques_abonnes_trajet',
      en: 'subscribers_by_trip_statistics',
      ar: 'احصائيات_المشتركين_حسب_الرحلة'
    },
    '2': {
      fr: 'statistiques_abonnes_etablissement',
      en: 'subscribers_by_establishment_statistics',
      ar: 'احصائيات_المشتركين_حسب_المؤسسة'
    },
    '3': {
      fr: 'statistiques_abonnes_tranche_km',
      en: 'subscribers_by_km_range_statistics',
      ar: 'احصائيات_المشتركين_حسب_المسافة'
    },
    '4': {
      fr: 'statistiques_abonnes_remise',
      en: 'subscribers_by_discount_statistics',
      ar: 'احصائيات_المشتركين_حسب_الخصم'
    },
    '5': {
      fr: 'statistiques_recettes_periode_vente',
      en: 'revenue_by_sale_period_statistics',
      ar: 'احصائيات_الايرادات_حسب_فترة_البيع'
    },
    '6': {
      fr: 'statistiques_recettes_agence',
      en: 'revenue_by_agency_statistics',
      ar: 'احصائيات_الايرادات_حسب_الوكالة'
    },
    '7': {
      fr: 'statistiques_abonnes_ligne',
      en: 'subscribers_by_line_statistics',
      ar: 'احصائيات_المشتركين_حسب_الخط'
    },
    '8': {
      fr: 'statistiques_recettes_gouvernorat',
      en: 'revenue_by_governorate_statistics',
      ar: 'احصائيات_الايرادات_حسب_الولاية'
    },
    '9': {
      fr: 'statistiques_recettes_methode_paiement',
      en: 'revenue_by_payment_method_statistics',
      ar: 'احصائيات_الايرادات_حسب_طريقة_الدفع'
    },
    '10': {
      fr: 'statistiques_abonnements_type',
      en: 'subscriptions_by_type_statistics',
      ar: 'احصائيات_الاشتراكات_حسب_النوع'
    },
    '11': {
      fr: 'statistiques_abonnes_point_vente',
      en: 'subscribers_by_sale_point_statistics',
      ar: 'احصائيات_المشتركين_حسب_نقطة_البيع'
    }
  };
  
  return filenames[statisticType]?.[currentLang] || 'statistiques_export';
};
